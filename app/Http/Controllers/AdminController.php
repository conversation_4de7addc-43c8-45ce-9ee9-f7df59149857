<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\Installment;
use App\Models\Advertisement;
use App\Models\ActivityLog;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Dashboard data aggregation
     */
    public function dashboard()
    {
        try {
            // System-wide statistics
            $stats = $this->getSystemStats();
            
            // Recent activities
            $recentActivities = $this->getRecentActivities();
            
            // Performance metrics
            $performanceMetrics = $this->getPerformanceMetrics();
            
            // Financial overview
            $financialOverview = $this->getFinancialOverview();
            
            // Branch performance
            $branchPerformance = $this->getBranchPerformance();
            
            // Alerts and notifications
            $alerts = $this->getSystemAlerts();

            return view('admin.dashboard', compact(
                'stats',
                'recentActivities',
                'performanceMetrics',
                'financialOverview',
                'branchPerformance',
                'alerts'
            ));
        } catch (\Exception $e) {
            \Log::error('Admin dashboard error: ' . $e->getMessage());
            return view('admin.dashboard')->with('error', 'Unable to load dashboard data.');
        }
    }

    /**
     * Get system-wide statistics
     */
    private function getSystemStats(): array
    {
        return [
            'total_members' => Member::count(),
            'active_members' => Member::where('status', 'active')->count(),
            'total_branches' => Branch::where('status', 'active')->count(),
            'total_users' => User::where('is_active', true)->count(),
            'total_loans' => Loan::count(),
            'active_loans' => Loan::active()->count(),
            'total_loan_amount' => Loan::sum('loan_amount'),
            'total_collections' => Installment::where('status', 'paid')->sum('installment_amount'),
            'total_savings' => SavingAccount::sum('monthly_amount'),
            'pending_applications' => LoanApplication::where('status', 'pending')->count(),
            'overdue_installments' => Installment::where('status', 'pending')
                ->where('installment_date', '<', now())->count(),
            'field_officers' => User::where('role', 'field_officer')->where('is_active', true)->count(),
            'managers' => User::where('role', 'manager')->where('is_active', true)->count(),
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities(int $limit = 20): \Illuminate\Database\Eloquent\Collection
    {
        return ActivityLog::with('user')
            ->latest()
            ->take($limit)
            ->get();
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'monthly_disbursements' => [
                'current' => Loan::where('loan_date', '>=', $currentMonth)->sum('loan_amount'),
                'previous' => Loan::whereBetween('loan_date', [$lastMonth, $currentMonth])->sum('loan_amount'),
            ],
            'monthly_collections' => [
                'current' => Installment::where('status', 'paid')
                    ->where('collection_date', '>=', $currentMonth)->sum('installment_amount'),
                'previous' => Installment::where('status', 'paid')
                    ->whereBetween('collection_date', [$lastMonth, $currentMonth])->sum('installment_amount'),
            ],
            'monthly_new_members' => [
                'current' => Member::where('created_at', '>=', $currentMonth)->count(),
                'previous' => Member::whereBetween('created_at', [$lastMonth, $currentMonth])->count(),
            ],
            'loan_approval_rate' => $this->calculateLoanApprovalRate(),
            'collection_rate' => $this->calculateCollectionRate(),
        ];
    }

    /**
     * Get financial overview
     */
    private function getFinancialOverview(): array
    {
        $totalDisbursed = Loan::sum('loan_amount');
        $totalCollected = Installment::where('status', 'paid')->sum('installment_amount');
        $totalOutstanding = Installment::where('status', 'pending')->sum('installment_amount');
        $totalSavings = SavingAccount::sum('monthly_amount');

        return [
            'total_disbursed' => $totalDisbursed,
            'total_collected' => $totalCollected,
            'total_outstanding' => $totalOutstanding,
            'total_savings' => $totalSavings,
            'portfolio_at_risk' => $this->calculatePortfolioAtRisk(),
            'return_on_assets' => $this->calculateReturnOnAssets(),
            'operational_self_sufficiency' => $this->calculateOperationalSelfSufficiency(),
        ];
    }

    /**
     * Get branch performance data
     */
    private function getBranchPerformance(): \Illuminate\Database\Eloquent\Collection
    {
        return Branch::with(['manager', 'users'])
            ->withCount(['members', 'loans'])
            ->get()
            ->map(function ($branch) {
                $branch->total_disbursed = $branch->loans()->sum('loan_amount');
                $branch->total_collected = $branch->getTotalCollections();
                $branch->active_loans = $branch->getTotalActiveLoans();
                $branch->overdue_amount = $this->getBranchOverdueAmount($branch);
                return $branch;
            });
    }

    /**
     * Get system alerts
     */
    private function getSystemAlerts(): array
    {
        $alerts = [];

        // High overdue rate alert
        $overdueRate = $this->calculateOverdueRate();
        if ($overdueRate > 10) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'High Overdue Rate',
                'message' => "Current overdue rate is {$overdueRate}%. Immediate attention required.",
                'action' => 'View Overdue Report',
                'url' => route('admin.reports.overdue'),
            ];
        }

        // Low collection rate alert
        $collectionRate = $this->calculateCollectionRate();
        if ($collectionRate < 90) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Low Collection Rate',
                'message' => "Collection rate has dropped to {$collectionRate}%.",
                'action' => 'View Collection Report',
                'url' => route('admin.reports.collections'),
            ];
        }

        // Pending applications alert
        $pendingCount = LoanApplication::where('status', 'pending')->count();
        if ($pendingCount > 50) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Pending Applications',
                'message' => "{$pendingCount} loan applications are pending review.",
                'action' => 'Review Applications',
                'url' => route('admin.loan-applications.index'),
            ];
        }

        return $alerts;
    }

    /**
     * User management CRUD operations
     */
    public function users(Request $request)
    {
        $query = User::with(['branch']);

        // Apply filters
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(20);
        $branches = Branch::where('status', 'active')->get();

        return view('admin.users.index', compact('users', 'branches'));
    }

    public function createUser()
    {
        $branches = Branch::where('status', 'active')->get();
        $members = Member::where('status', 'active')->whereDoesntHave('user')->get();
        
        return view('admin.users.create', compact('branches', 'members'));
    }

    public function storeUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,manager,field_officer,member',
            'branch_id' => 'nullable|exists:branches,id',
            'member_id' => 'nullable|exists:members,id',
            'is_active' => 'boolean',
            'phone' => 'nullable|string|max:15',
        ], [
            'name.required' => 'Name is required.',
            'email.required' => 'Email address is required.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'role.required' => 'Role selection is required.',
        ]);

        // Additional validation based on role
        if ($validated['role'] === 'member' && !$validated['member_id']) {
            return back()->withErrors(['member_id' => 'Member selection is required for member role.'])->withInput();
        }

        if (in_array($validated['role'], ['manager', 'field_officer']) && !$validated['branch_id']) {
            return back()->withErrors(['branch_id' => 'Branch selection is required for this role.'])->withInput();
        }

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => $validated['role'],
                'branch_id' => $validated['branch_id'],
                'member_id' => $validated['member_id'],
                'is_active' => $validated['is_active'] ?? true,
                'phone' => $validated['phone'],
                'created_by' => auth()->id(),
            ]);

            // Update member's user association if applicable
            if ($validated['member_id']) {
                Member::where('id', $validated['member_id'])->update(['user_id' => $user->id]);
            }

            $this->logActivity('user_created', "User {$user->name} created with role {$user->role}", $user);

            DB::commit();

            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('User creation failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to create user. Please try again.')->withInput();
        }
    }

    /**
     * Business logic helper methods
     */
    private function calculateLoanApprovalRate(): float
    {
        $totalApplications = LoanApplication::count();
        if ($totalApplications === 0) return 0;

        $approvedApplications = LoanApplication::where('status', 'approved')->count();
        return round(($approvedApplications / $totalApplications) * 100, 2);
    }

    private function calculateCollectionRate(): float
    {
        $totalDue = Installment::where('installment_date', '<=', now())->sum('installment_amount');
        if ($totalDue === 0) return 100;

        $totalCollected = Installment::where('status', 'paid')->sum('installment_amount');
        return round(($totalCollected / $totalDue) * 100, 2);
    }

    private function calculatePortfolioAtRisk(): float
    {
        $totalOutstanding = Installment::where('status', 'pending')->sum('installment_amount');
        if ($totalOutstanding === 0) return 0;

        $overdueAmount = Installment::where('status', 'pending')
            ->where('installment_date', '<', now()->subDays(30))
            ->sum('installment_amount');

        return round(($overdueAmount / $totalOutstanding) * 100, 2);
    }

    private function calculateReturnOnAssets(): float
    {
        $totalAssets = Loan::sum('loan_amount') + SavingAccount::sum('monthly_amount');
        if ($totalAssets === 0) return 0;

        $netIncome = $this->calculateNetIncome();
        return round(($netIncome / $totalAssets) * 100, 2);
    }

    private function calculateOperationalSelfSufficiency(): float
    {
        $operatingRevenue = $this->calculateOperatingRevenue();
        $operatingExpenses = $this->calculateOperatingExpenses();

        if ($operatingExpenses === 0) return 0;
        return round(($operatingRevenue / $operatingExpenses) * 100, 2);
    }

    private function calculateOverdueRate(): float
    {
        $totalInstallments = Installment::where('installment_date', '<=', now())->count();
        if ($totalInstallments === 0) return 0;

        $overdueInstallments = Installment::where('status', 'pending')
            ->where('installment_date', '<', now())
            ->count();

        return round(($overdueInstallments / $totalInstallments) * 100, 2);
    }

    private function getBranchOverdueAmount(Branch $branch): float
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', now())
        ->sum('installment_amount');
    }

    private function calculateNetIncome(): float
    {
        // Simplified calculation - in real scenario, this would be more complex
        $interestIncome = Installment::where('status', 'paid')->sum('installment_amount') - Loan::sum('loan_amount');
        $operatingExpenses = $this->calculateOperatingExpenses();

        return $interestIncome - $operatingExpenses;
    }

    private function calculateOperatingRevenue(): float
    {
        // Interest income from loans
        return Installment::where('status', 'paid')->sum('installment_amount') - Loan::sum('loan_amount');
    }

    private function calculateOperatingExpenses(): float
    {
        // Simplified - in real scenario, this would include staff costs, admin costs, etc.
        return User::where('is_active', true)->count() * 30000; // Estimated monthly cost per employee
    }

    /**
     * Log activity
     */
    private function logActivity(string $action, string $description, ?User $user = null, array $metadata = []): void
    {
        try {
            ActivityLog::create([
                'user_id' => $user?->id ?? auth()->id(),
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => array_merge($metadata, [
                    'timestamp' => now()->toISOString(),
                    'admin_user' => auth()->user()->name,
                ]),
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }
}
