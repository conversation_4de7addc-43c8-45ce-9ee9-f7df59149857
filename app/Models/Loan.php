<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_application_id',
        'loan_amount',
        'interest_rate',
        'loan_duration_months',
        'repayment_method',
        'loan_date',
        'advance_payment',
        'first_installment_date',
        'last_installment_date',
    ];

    protected $casts = [
        'loan_amount' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'advance_payment' => 'decimal:2',
        'loan_date' => 'date',
        'first_installment_date' => 'date',
        'last_installment_date' => 'date',
    ];

    /**
     * Relationships
     */
    public function loanApplication()
    {
        return $this->belongsTo(LoanApplication::class);
    }

    public function member()
    {
        return $this->hasOneThrough(Member::class, LoanApplication::class, 'id', 'id', 'loan_application_id', 'member_id');
    }

    public function installments()
    {
        return $this->hasMany(Installment::class);
    }

    /**
     * Business logic methods
     */
    public function calculateRemainingAmount(): float
    {
        $paidAmount = $this->installments()->where('status', 'paid')->sum('installment_amount');
        $totalAmount = $this->installments()->sum('installment_amount');

        return $totalAmount - $paidAmount;
    }

    public function getOverdueInstallments()
    {
        return $this->installments()
            ->where('status', 'pending')
            ->where('installment_date', '<', now())
            ->orderBy('installment_date')
            ->get();
    }

    public function getNextInstallmentDate(): ?Carbon
    {
        $nextInstallment = $this->installments()
            ->where('status', 'pending')
            ->orderBy('installment_date')
            ->first();

        return $nextInstallment ? $nextInstallment->installment_date : null;
    }

    public function getTotalPaidAmount(): float
    {
        return $this->installments()->where('status', 'paid')->sum('installment_amount');
    }

    public function getTotalPendingAmount(): float
    {
        return $this->installments()->where('status', 'pending')->sum('installment_amount');
    }

    public function getCompletionPercentage(): float
    {
        $totalAmount = $this->installments()->sum('installment_amount');
        $paidAmount = $this->getTotalPaidAmount();

        return $totalAmount > 0 ? ($paidAmount / $totalAmount) * 100 : 0;
    }

    public function isFullyPaid(): bool
    {
        return $this->installments()->where('status', 'pending')->count() === 0;
    }

    public function isOverdue(): bool
    {
        return $this->getOverdueInstallments()->count() > 0;
    }

    public function getDaysOverdue(): int
    {
        $oldestOverdue = $this->getOverdueInstallments()->first();

        return $oldestOverdue ? $oldestOverdue->installment_date->diffInDays(now()) : 0;
    }

    public function generateInstallments(): void
    {
        // Delete existing installments if any
        $this->installments()->delete();

        $totalAmount = $this->loan_amount - $this->advance_payment;
        $monthlyInterestRate = $this->interest_rate / 100 / 12;

        // Calculate installment amount based on repayment method
        if ($this->repayment_method === 'weekly') {
            $numberOfInstallments = $this->loan_duration_months * 4; // 4 weeks per month
            $installmentInterval = 7; // days
        } elseif ($this->repayment_method === 'monthly') {
            $numberOfInstallments = $this->loan_duration_months;
            $installmentInterval = 30; // days
        } else {
            $numberOfInstallments = $this->loan_duration_months * 2; // bi-weekly
            $installmentInterval = 14; // days
        }

        // Calculate installment amount using compound interest formula
        if ($monthlyInterestRate > 0) {
            $installmentAmount = $totalAmount *
                ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $this->loan_duration_months)) /
                (pow(1 + $monthlyInterestRate, $this->loan_duration_months) - 1);

            // Adjust for payment frequency
            if ($this->repayment_method === 'weekly') {
                $installmentAmount = $installmentAmount / 4;
            } elseif ($this->repayment_method === 'bi_weekly') {
                $installmentAmount = $installmentAmount / 2;
            }
        } else {
            $installmentAmount = $totalAmount / $numberOfInstallments;
        }

        $installmentAmount = round($installmentAmount, 2);

        // Generate installments
        $currentDate = $this->first_installment_date;

        for ($i = 1; $i <= $numberOfInstallments; $i++) {
            // Adjust last installment to account for rounding differences
            $amount = $installmentAmount;
            if ($i === $numberOfInstallments) {
                $totalGenerated = $installmentAmount * ($numberOfInstallments - 1);
                $totalWithInterest = $this->calculateTotalRepaymentAmount();
                $amount = $totalWithInterest - $totalGenerated;
            }

            $this->installments()->create([
                'installment_no' => $i,
                'amount' => $amount,
                'installment_date' => $currentDate,
                'status' => 'pending',
            ]);

            $currentDate = $currentDate->addDays($installmentInterval);
        }
    }

    public function calculateTotalRepaymentAmount(): float
    {
        $principal = $this->loan_amount - $this->advance_payment;
        $monthlyInterestRate = $this->interest_rate / 100 / 12;

        if ($monthlyInterestRate == 0) {
            return $principal + $this->advance_payment;
        }

        $monthlyPayment = $principal *
            ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $this->loan_duration_months)) /
            (pow(1 + $monthlyInterestRate, $this->loan_duration_months) - 1);

        return ($monthlyPayment * $this->loan_duration_months) + $this->advance_payment;
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->whereHas('installments', function ($q) {
            $q->where('status', 'pending');
        });
    }

    public function scopeCompleted($query)
    {
        return $query->whereDoesntHave('installments', function ($q) {
            $q->where('status', 'pending');
        });
    }

    public function scopeOverdue($query)
    {
        return $query->whereHas('installments', function ($q) {
            $q->where('status', 'pending')
              ->where('installment_date', '<', now());
        });
    }

    public function scopeByMember($query, $memberId)
    {
        return $query->whereHas('loanApplication', function ($q) use ($memberId) {
            $q->where('member_id', $memberId);
        });
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->whereHas('loanApplication.member', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        });
    }

    public function scopeByRepaymentMethod($query, $method)
    {
        return $query->where('repayment_method', $method);
    }

    /**
     * Accessors
     */
    public function getStatusAttribute(): string
    {
        if ($this->isFullyPaid()) {
            return 'Completed';
        } elseif ($this->isOverdue()) {
            return 'Overdue';
        } else {
            return 'Active';
        }
    }

    public function getFormattedLoanAmountAttribute(): string
    {
        return '৳ ' . number_format($this->loan_amount, 2);
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return '৳ ' . number_format($this->calculateRemainingAmount(), 2);
    }

    public function getFormattedAdvancePaymentAttribute(): string
    {
        return '৳ ' . number_format($this->advance_payment, 2);
    }

    public function getRepaymentMethodDisplayAttribute(): string
    {
        return match($this->repayment_method) {
            'weekly' => 'Weekly',
            'bi_weekly' => 'Bi-Weekly',
            'monthly' => 'Monthly',
            default => 'Unknown'
        };
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'loan_application_id' => 'required|exists:loan_applications,id',
            'loan_amount' => 'required|numeric|min:1000|max:500000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,bi_weekly,monthly',
            'loan_date' => 'required|date',
            'advance_payment' => 'nullable|numeric|min:0',
            'first_installment_date' => 'required|date|after:loan_date',
            'last_installment_date' => 'required|date|after:first_installment_date',
        ];
    }
}
