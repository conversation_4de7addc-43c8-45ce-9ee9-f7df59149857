<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavingAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'saving_type',
        'joint_photo',
        'nominee_name',
        'nominee_relation',
        'saving_method',
        'monthly_amount',
        'fdr_amount',
        'start_date',
        'created_by',
    ];

    protected $casts = [
        'monthly_amount' => 'decimal:2',
        'fdr_amount' => 'decimal:2',
        'start_date' => 'date',
    ];

    /**
     * Relationships
     */
    public function member()
    {
        return $this->belongsTo(Member::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function transactions()
    {
        return $this->hasMany(SavingTransaction::class, 'saving_account_id');
    }

    /**
     * Business logic methods
     */
    public function calculateCurrentBalance(): float
    {
        $deposits = $this->transactions()->where('transaction_type', 'deposit')->sum('amount');
        $withdrawals = $this->transactions()->where('transaction_type', 'withdrawal')->sum('amount');

        return $this->opening_balance + $deposits - $withdrawals;
    }

    public function addDeposit(float $amount, User $enteredBy, string $description = null): SavingTransaction
    {
        $transaction = $this->transactions()->create([
            'transaction_type' => 'deposit',
            'amount' => $amount,
            'description' => $description ?? 'Deposit',
            'transaction_date' => now(),
            'entered_by' => $enteredBy->id,
            'balance_after' => $this->current_balance + $amount,
        ]);

        $this->update(['current_balance' => $this->current_balance + $amount]);

        return $transaction;
    }

    public function addWithdrawal(float $amount, User $enteredBy, string $description = null): SavingTransaction
    {
        if ($this->current_balance - $amount < $this->minimum_balance) {
            throw new \Exception('Insufficient balance. Minimum balance required: ৳' . number_format($this->minimum_balance, 2));
        }

        $transaction = $this->transactions()->create([
            'transaction_type' => 'withdrawal',
            'amount' => $amount,
            'description' => $description ?? 'Withdrawal',
            'transaction_date' => now(),
            'entered_by' => $enteredBy->id,
            'balance_after' => $this->current_balance - $amount,
        ]);

        $this->update(['current_balance' => $this->current_balance - $amount]);

        return $transaction;
    }

    public function getTransactionHistory($limit = 50)
    {
        return $this->transactions()
                   ->with('enteredBy')
                   ->orderBy('transaction_date', 'desc')
                   ->limit($limit)
                   ->get();
    }

    public function calculateMonthlyInterest(): float
    {
        $monthlyRate = $this->interest_rate / 100 / 12;
        return $this->current_balance * $monthlyRate;
    }

    public function applyMonthlyInterest(User $enteredBy): ?SavingTransaction
    {
        if ($this->interest_rate <= 0) {
            return null;
        }

        $interestAmount = $this->calculateMonthlyInterest();

        if ($interestAmount > 0) {
            return $this->addDeposit(
                $interestAmount,
                $enteredBy,
                'Monthly interest credit'
            );
        }

        return null;
    }

    public function canWithdraw(float $amount): bool
    {
        return ($this->current_balance - $amount) >= $this->minimum_balance;
    }

    public function getAvailableBalance(): float
    {
        return max(0, $this->current_balance - $this->minimum_balance);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query; // All accounts are considered active since there's no status column
    }

    public function scopeInactive($query)
    {
        return $query->whereRaw('1 = 0'); // Return empty result
    }

    public function scopeClosed($query)
    {
        return $query->whereRaw('1 = 0'); // Return empty result
    }

    public function scopeByMember($query, $memberId)
    {
        return $query->where('member_id', $memberId);
    }

    public function scopeByAccountType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    public function scopeWithMinimumBalance($query, $amount)
    {
        return $query->where('current_balance', '>=', $amount);
    }

    /**
     * Accessors
     */
    public function getStatusBadgeAttribute(): string
    {
        return '<span class="badge bg-success">Active</span>'; // All accounts are active
    }

    public function getFormattedCurrentBalanceAttribute(): string
    {
        return '৳ ' . number_format($this->current_balance, 2);
    }

    public function getFormattedOpeningBalanceAttribute(): string
    {
        return '৳ ' . number_format($this->opening_balance, 2);
    }

    public function getBalanceAsOf($date): float
    {
        $transactions = $this->transactions()
            ->where('transaction_date', '<=', $date)
            ->orderBy('transaction_date')
            ->get();

        $balance = $this->opening_balance;

        foreach ($transactions as $transaction) {
            if ($transaction->transaction_type === 'deposit') {
                $balance += $transaction->amount;
            } else {
                $balance -= $transaction->amount;
            }
        }

        return $balance;
    }

    public function getFormattedMinimumBalanceAttribute(): string
    {
        return '৳ ' . number_format($this->minimum_balance, 2);
    }

    public function getFormattedAvailableBalanceAttribute(): string
    {
        return '৳ ' . number_format($this->getAvailableBalance(), 2);
    }

    public function getAccountTypeDisplayAttribute(): string
    {
        return match($this->account_type) {
            'general' => 'General Savings',
            'fixed' => 'Fixed Deposit',
            'recurring' => 'Recurring Deposit',
            'current' => 'Current Account',
            default => ucfirst($this->account_type ?? 'Unknown')
        };
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'member_id' => 'required|exists:members,id',
            'account_number' => 'required|string|unique:saving_accounts,account_number',
            'account_type' => 'required|in:general,fixed,recurring,current',
            'opening_balance' => 'required|numeric|min:0',
            'interest_rate' => 'nullable|numeric|min:0|max:20',
            'minimum_balance' => 'nullable|numeric|min:0',

            'opened_date' => 'required|date',
            'created_by' => 'required|exists:users,id',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($account) {
            if (!$account->account_number) {
                $account->account_number = 'SAV-' . strtoupper(uniqid());
            }

            if (!$account->current_balance) {
                $account->current_balance = $account->opening_balance;
            }

            if (!$account->opened_date) {
                $account->opened_date = now();
            }
        });
    }
}
