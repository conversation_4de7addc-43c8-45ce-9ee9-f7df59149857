<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'manager_id',
    ];

    /**
     * Relationships
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function members()
    {
        return $this->hasMany(Member::class);
    }

    public function loanApplications()
    {
        return $this->hasManyThrough(LoanApplication::class, Member::class);
    }

    public function loans()
    {
        return $this->hasManyThrough(
            Loan::class,
            LoanApplication::class,
            'member_id', // Foreign key on loan_applications table
            'loan_application_id', // Foreign key on loans table
            'id', // Local key on branches table (this is wrong, should be member_id)
            'id' // Local key on loan_applications table
        );
    }

    // Helper method to get loans for this branch
    public function getBranchLoans()
    {
        return Loan::whereHas('loanApplication.member', function ($query) {
            $query->where('branch_id', $this->id);
        });
    }

    public function savingAccounts()
    {
        return $this->hasManyThrough(SavingAccount::class, Member::class);
    }

    public function branchTransactions()
    {
        return $this->hasMany(BranchTransaction::class);
    }

    /**
     * Business logic methods
     */
    public function getTotalActiveLoans(): int
    {
        return $this->loans()
            ->whereHas('installments', function ($query) {
                $query->where('status', 'pending');
            })
            ->count();
    }

    public function getTotalCollections(): float
    {
        return $this->loans()
            ->with('installments')
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            });
    }

    public function getFieldOfficers()
    {
        return $this->users()->where('role', 'field_officer')->get();
    }

    public function getMonthlyLoanDisbursement($month = null, $year = null): float
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        return $this->loans()
            ->whereMonth('loan_date', $month)
            ->whereYear('loan_date', $year)
            ->sum('loan_amount');
    }

    public function getMonthlyCollection($month = null, $year = null): float
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        return $this->loans()
            ->with(['installments' => function ($query) use ($month, $year) {
                $query->where('status', 'paid')
                    ->whereMonth('collection_date', $month)
                    ->whereYear('collection_date', $year);
            }])
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->sum('amount');
            });
    }

    public function getOverdueLoansCount(): int
    {
        return $this->loans()
            ->whereHas('installments', function ($query) {
                $query->where('status', 'pending')
                    ->where('installment_date', '<', now());
            })
            ->count();
    }

    public function getTotalSavingsBalance(): float
    {
        return $this->savingAccounts()->sum('current_balance');
    }

    /**
     * Scopes
     */
    public function scopeWithManager($query)
    {
        return $query->with('manager');
    }

    public function scopeActive($query)
    {
        return $query->whereHas('manager', function ($q) {
            $q->where('is_active', true);
        });
    }
}
